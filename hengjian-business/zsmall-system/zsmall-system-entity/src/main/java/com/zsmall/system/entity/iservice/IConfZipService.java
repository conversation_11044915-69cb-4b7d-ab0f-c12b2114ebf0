package com.zsmall.system.entity.iservice;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.zsmall.system.entity.domain.ConfZip;
import com.zsmall.system.entity.domain.vo.confZip.ConfZipVo;
import com.zsmall.system.entity.mapper.ConfZipMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2024/4/12 16:27
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IConfZipService extends ServiceImpl<ConfZipMapper, ConfZip> {
    @Resource
    private ConfZipMapper confZipMapper;
    public String transZip(String postalCode, String state, String country, String city) {
        // 校验是否加密
        if(!containsAsterisk(postalCode)){
            return postalCode;
        }
        String zip = postalCode;

        List<ConfZip> confZipList = confZipMapper.selectConfZipByStateAndCounty(state, country);
        if (CollectionUtils.isNotEmpty(confZipList)) {
            String prefixZip = postalCode.substring(0, 2);
            List<ConfZip> subConfZipList = confZipList.stream().filter(item -> item.getZip().startsWith(prefixZip)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(subConfZipList)) {
                String prefixCity = city.substring(0, 2);
                ConfZip confZip = subConfZipList.stream().filter(item -> StringUtils.isNotEmpty(item.getPrimaryCity()) &&
                    item.getPrimaryCity().startsWith(prefixCity)).findAny().orElse(null);
                if (null != confZip) {
                    zip = confZip.getZip().substring(0, 3) + "**";
                } else {
                    confZip = subConfZipList.get(0);
                    zip = confZip.getZip().substring(0, 3) + "**";
                }
            }
        }

        return zip;
    }

    public ConfZip getConfZip(String postalCode, String state, String country, String city) {

        List<ConfZip> confZipList = confZipMapper.selectConfZipByStateAndCounty(state, country);
        if (CollectionUtils.isNotEmpty(confZipList)) {
            String prefixZip = postalCode.substring(0, 2);
            List<ConfZip> subConfZipList = confZipList.stream().filter(item -> item.getZip().startsWith(prefixZip)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(subConfZipList)) {
                String prefixCity = city.substring(0, 2);
                ConfZip confZip = subConfZipList.stream().filter(item -> StringUtils.isNotEmpty(item.getPrimaryCity()) &&
                    item.getPrimaryCity().startsWith(prefixCity)).findAny().orElse(null);
                if (null != confZip) {
                    return confZip;

                } else {
                    return subConfZipList.get(0);

                }
            }
        }

        return null;
    }
    public  boolean containsAsterisk(String str) {
        // 创建包含*的正则表达式
        Pattern pattern = Pattern.compile("\\*");
        // 创建Matcher对象
        Matcher matcher = pattern.matcher(str);
        // 使用find()方法查找*字符
        return matcher.find();
    }

    /**
     * 功能描述：按州名称获取州代码
     *
     * @param state 状态
     * @return {@link ConfZip }
     * <AUTHOR>
     * @date 2024/04/24
     */
    public ConfZip getStateCodeByStateName(String state) {
        LambdaQueryWrapper<ConfZip> eq = new LambdaQueryWrapper<ConfZip>().eq(ConfZip::getStateName, state).last("limit 1");
        return confZipMapper.selectOne(eq);
    }

    public ConfZip getByCountry(String state) {
        LambdaQueryWrapper<ConfZip> eq = new LambdaQueryWrapper<ConfZip>().eq(ConfZip::getCountry, state).last("limit 1");
        return confZipMapper.selectOne(eq);
    }

    /**
     * 功能描述：按州名称获取州代码
     *
     * @param stateCode 状态
     * @return {@link ConfZip }
     * <AUTHOR>
     * @date 2024/04/24
     */
    public ConfZip getStateCodeByStateCode(String stateCode) {
        LambdaQueryWrapper<ConfZip> eq = new LambdaQueryWrapper<ConfZip>().eq(ConfZip::getStateCode, stateCode).last("limit 1");
        return confZipMapper.selectOne(eq);
    }

    /**
     * 功能描述：按城市获取州代码
     *
     * @param city 城市
     * @return {@link ConfZip }
     * <AUTHOR>
     * @date 2024/04/24
     */
    public ConfZip getStateCodeByCity(String city) {
        LambdaQueryWrapper<ConfZip> eq = new LambdaQueryWrapper<ConfZip>().eq(ConfZip::getPrimaryCity, city).last("limit 1");
        return confZipMapper.selectOne(eq);
    }

    /**
     * 获取层级关系
     * @return
     */
    public List<ConfZipVo> queryList() {
        List<String> countryList=List.of("US","DE");
        List<ConfZipVo> zipVoList=new ArrayList<>();
        for (int i = 0; i < countryList.size(); i++) {
            ConfZipVo confZipVo=new ConfZipVo();
            confZipVo.setId(i);
            confZipVo.setCountry(countryList.get(i));
            String country = countryList.get(i);
            //查询所有的州
        LambdaQueryWrapper<ConfZip> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConfZip::getDecommissioned,0);
            queryWrapper.eq(ConfZip::getCountry,country);
        queryWrapper.eq(ConfZip::getZipType,"STANDARD");
        queryWrapper.isNotNull(ConfZip::getStateName);
        queryWrapper.groupBy(ConfZip::getStateName);
        List<ConfZip> confZips = confZipMapper.selectList(queryWrapper);
            ArrayList<ConfZipVo.State> states = new ArrayList<>();
            confZips.forEach(s->{
                ConfZipVo.State state=new ConfZipVo.State();
                state.setId(s.getId());
                state.setStateName(s.getStateName());
                states.add(state);

            });
            confZipVo.setStateList(states);
            confZipVo.getStateList().forEach(s->{
            LambdaQueryWrapper<ConfZip> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            //查询所有的州
            lambdaQueryWrapper.eq(ConfZip::getStateName,s.getStateName());
                lambdaQueryWrapper.eq(ConfZip::getCountry,country);
            lambdaQueryWrapper.eq(ConfZip::getDecommissioned,0);
            lambdaQueryWrapper.isNotNull(ConfZip::getStateName);
            lambdaQueryWrapper.eq(ConfZip::getZipType,"STANDARD");
            List<ConfZip> confZip = confZipMapper.selectList(lambdaQueryWrapper);
            List<ConfZipVo.CityVo> cityVos = BeanUtil.copyToList(confZip, ConfZipVo.CityVo.class);
            s.setCityList(cityVos);
        });

            zipVoList.add(confZipVo);

        }
        return zipVoList;
    }
}
