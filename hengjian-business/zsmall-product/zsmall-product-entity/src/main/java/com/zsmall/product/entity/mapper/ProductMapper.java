package com.zsmall.product.entity.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.tenant.mapper.TenantMapperPlus;
import com.zsmall.common.enums.product.SupportedLogisticsEnum;
import com.zsmall.product.entity.domain.Product;
import com.zsmall.product.entity.domain.bo.member.RulePriceQueryBo;
import com.zsmall.product.entity.domain.bo.product.ProductQueryBo;
import com.zsmall.product.entity.domain.dto.product.ProductOnShelfDTO;
import com.zsmall.product.entity.domain.vo.product.ProductExportDto;
import com.zsmall.product.entity.domain.vo.product.ProductNewestVo;
import com.zsmall.product.entity.domain.vo.product.ProductVo;
import com.zsmall.product.entity.domain.vo.productMapping.ImportReadyVo;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 商品SPUMapper接口
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
public interface ProductMapper extends TenantMapperPlus<Product, ProductVo> {

    /** 查询SPU编号是否已存在 */
    @InterceptorIgnore(tenantLine = "true")
    boolean existProductCode(@Param("productCode") String productCode);

    IPage<Product> getProductPage2Label(Page<Product> queryPage, @Param("queryValue") String queryValue, @Param("labelIdList") List<Long> labelIdList);

    /**
     * 分页查询商品列表
     * @param queryPage
     * @param queryBo
     * @return
     */
    IPage<Product> queryPageList(Page<Product> queryPage, @Param("queryBo") ProductQueryBo queryBo);

    @InterceptorIgnore(tenantLine = "true")
    Product queryByWrapperNoTenant(@Param(Constants.WRAPPER) Wrapper<Product> queryWrapper);

    @InterceptorIgnore(tenantLine = "true")
    Product queryByProductSkuCodeNoDelete(@Param("productSkuCode") String productSkuCode);

    @InterceptorIgnore(tenantLine = "true")
    List<Product> queryByProductCodesIncludeDel(@Param("productCodes") Collection productCodes);

    @InterceptorIgnore(tenantLine = "true")
    SupportedLogisticsEnum querySupportedLogisticsByProductSkuCode(@Param("productSkuCode") String productSkuCode);

    Product queryByProductSkuCode(@Param("productCode") String productCode);

    @InterceptorIgnore(tenantLine = "true")
    Product queryByIdIncludeDelete(@Param("productId") Long productId);

    List<Product> queryByIdsIncludeDel(@Param("productIds") Long... productIds);

    @InterceptorIgnore(tenantLine = "true")
    ImportReadyVo queryImportReadyInfo(@Param("disId") String disId, @Param("productCode") String productCode, @Param("channelType") String channelType);

    @InterceptorIgnore(tenantLine = "true")
    List<ProductNewestVo> queryNewestProduct(Page<Product> queryPage,@Param("isZjHj") boolean isZjHj);

    ProductOnShelfDTO getProductOnShelfDTOByProductSkuCode(@Param("productSkuCode") String productSkuCode);

    @InterceptorIgnore(tenantLine = "true")
    List<Product> queryNormalProductForES();

    List<String> getAllBizArkProduct();

    @InterceptorIgnore(tenantLine = "true")
    void updateProductInventoryPushTime(String productSkuCode);

    IPage<Product> queryPageListForMemberLevel(Page<Product> queryPage,@Param("queryBo") RulePriceQueryBo queryBo);

    List<ProductExportDto> queryExportDate(@Param("page") Integer page, @Param("pageSize") Integer pageSize, @Param("queryBo") ProductQueryBo bo,@Param("tenantId") String tenantId);

    List<ProductExportDto> queryExportInventoryDimension(@Param("page") Integer page, @Param("pageSize") Integer pageSize, @Param("queryBo") ProductQueryBo bo,@Param("tenantId") String tenantId);
    List<ProductExportDto> queryExportInventoryDimensionV2(@Param("queryBo") ProductQueryBo bo,@Param("tenantId") String tenantId);
    Integer getExportInventoryDimensionCount(@Param("queryBo") ProductQueryBo bo, @Param("tenantId") String tenantId);

    Integer getProductExportCount(@Param("queryBo") ProductQueryBo bo,@Param("tenantId") String tenantId);

    List<Product> getByProductSkuCodes(@Param("productSkuCodes") Set<String> productSkuCodes);
    boolean isAllSkuOffShelfBySpu(String productCode);
}
