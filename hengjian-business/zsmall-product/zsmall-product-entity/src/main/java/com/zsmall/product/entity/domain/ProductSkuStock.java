package com.zsmall.product.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.common.enums.common.GlobalStateEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 商品SKU库存对象 product_sku_stock
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_sku_stock")
@Accessors(chain = true)
public class ProductSkuStock extends NoDeptTenantEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 库存所在国家（不存在于数据库，仅作为参数携带）
     */
    @TableField(exist = false)
    private String country;

    /**
     * 库存编号
     */
    private String stockCode;

    /**
     * 总库存
     */
    private Integer stockTotal;

    /**
     * 预留库存
     */
    private Integer stockReserved;

    /**
     * 自提可用库存
     */
    private Integer stockAvailable;
    /**
     * 代发可用库存标识 0单仓,1非单仓(等于自提库存)
     */
    private Integer dropShippingStockAvailable;

    /**
     * 库存状态（0-停用，1-启用等）
     */
    private GlobalStateEnum stockState;

    /**
     * 第三方系统库存单位
     */
    private String erpSku;

    /**
     * SPU唯一编号
     */
    private String productCode;

    /**
     * SKU唯一编号（ItemNo.）
     */
    private String productSkuCode;

    /**
     * 仓库系统编号
     */
    private String warehouseSystemCode;

    /**
     * 关联物流模板编号
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String logisticsTemplateNo;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
    /**
     * 自提锁货总数量
     */
    private Integer pickupLockUsed;
    /**
     * 代发锁货总数量
     */
    private Integer dropShippingLockUsed;
    /**
     * 锁货异常码(拉库存的时候去标识)
     */
    private Integer lockExceptionCode;



}
