package com.zsmall.system.entity.iservice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.common.enums.bill.BillStateEnum;
import com.zsmall.common.enums.bill.GenerateStateEnum;
import com.zsmall.common.enums.bill.WithdrawalStateEnum;
import com.zsmall.system.entity.domain.Bill;
import com.zsmall.system.entity.mapper.BillMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【bill(账单表)】的数据库操作Service实现
 * @createDate 2022-11-28 19:39:05
 */
@Slf4j
@Service
public class IBillService extends ServiceImpl<BillMapper, Bill> {

    @InMethodLog("根据实体查询账单")
    public List<Bill> queryByEntity(Bill queryEntity) {
        LambdaQueryWrapper<Bill> queryWrapper = getQueryWrapper(queryEntity);

        if (TenantType.Manager.equals(LoginHelper.getTenantTypeEnum())) {
            return TenantHelper.ignore(() -> this.list(queryWrapper));
        } else {
            return this.list(queryWrapper);
        }
    }

    @InMethodLog("根据实体查询账单（无视租户）")
    public List<Bill> queryByEntityNotTenant(Bill queryEntity) {
        LambdaQueryWrapper<Bill> queryWrapper = getQueryWrapper(queryEntity);
        return TenantHelper.ignore(() -> this.list(queryWrapper));
    }

    @InMethodLog("根据实体计数账单")
    public long countByEntity(Bill queryEntity) {
        LambdaQueryWrapper<Bill> queryWrapper = getQueryWrapper(queryEntity);
        return this.count(queryWrapper);
    }

    @InMethodLog("根据实体计数账单（无视租户）")
    public long countByEntityNotTenant(Bill queryEntity) {
        LambdaQueryWrapper<Bill> queryWrapper = getQueryWrapper(queryEntity);
        return TenantHelper.ignore(() -> this.count(queryWrapper));
    }

    @InMethodLog("分页查询账单")
    public Page<Bill> queryPage(String queryType, String queryValue,
                                LocalDateTime begin, LocalDateTime end, Page<Bill> queryPage) {
        if (TenantType.Manager.equals(LoginHelper.getTenantTypeEnum())) {
            return TenantHelper.ignore(() -> baseMapper.queryPage(queryType, queryValue, begin, end, queryPage));
        } else {
            return baseMapper.queryPage(queryType, queryValue, begin, end, queryPage);
        }
    }

    @InMethodLog("查询账单列表")
    public List<Bill> queryList(String queryType, String queryValue,
                                LocalDateTime begin, LocalDateTime end) {
        if (TenantType.Manager.equals(LoginHelper.getTenantTypeEnum())) {
            return TenantHelper.ignore(() -> baseMapper.queryPage(queryType, queryValue, begin, end));
        } else {
            return baseMapper.queryPage(queryType, queryValue, begin, end);
        }
    }

    @InMethodLog("获取供应商可结余账单列表")
    public List<Bill> queryBalanceBillList(String tenantId) {
        LambdaQueryWrapper<Bill> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotNull(tenantId), Bill::getTenantId, tenantId);
        queryWrapper.eq(Bill::getBillState, BillStateEnum.Settled);
        queryWrapper.eq(Bill::getWithdrawalState, WithdrawalStateEnum.NotWithdrawal);
        queryWrapper.orderByDesc(Bill::getCreateTime);
        return this.list(queryWrapper);
    }

    @InMethodLog("根据周期编号和账单状态查询账单")
    public List<Bill> queryByBillCycleNoAndBillStatus(String billCycleNo, Integer billStatus) {
        return lambdaQuery().eq(Bill::getBillCycleNo, billCycleNo).eq(Bill::getBillState, billStatus).list();
    }

    @InMethodLog("查询缺少指定摘要的账单")
    public List<Bill> queryLacksAbstract(String billNo) {
        return baseMapper.queryLacksAbstract(billNo);
    }

    @InMethodLog("统计循环保证金")
    public BigDecimal sumCircularDeposit(String tenantId) {
        return baseMapper.sumCircularDeposit(tenantId);
    }

    @InMethodLog("统计所有已结算但未提现的金额")
    public BigDecimal sumUnsettledTotalAmount(String tenantId) {
        return baseMapper.sumUnsettledTotalAmount(tenantId);
    }

    @InMethodLog("通过账单编号集合统计账单集合")
    public List<Bill> getByBillNoList(List<String> billNoList) {
        if (CollUtil.size(billNoList) == 0) {
            return new ArrayList<>();
        }
        return lambdaQuery().in(Bill::getBillNo, billNoList).list();
    }

    @InMethodLog("通过账单id集合获取账单数据")
    public List<Bill> getListByIdList(List<Long> IdList) {
        if (CollUtil.size(IdList) == 0) {
            return new ArrayList<>();
        }
        return lambdaQuery().in(Bill::getId, IdList).list();
    }

    private LambdaQueryWrapper<Bill> getQueryWrapper(Bill queryEntity) {
        Long previousBillId = queryEntity.getPreviousBillId();
        String tenantId = queryEntity.getTenantId();
        String billNo = queryEntity.getBillNo();
        String billCycleNo = queryEntity.getBillCycleNo();
        LocalDateTime settlementDateTime = queryEntity.getSettlementDateTime();
        LocalDateTime settlementCycleBegin = queryEntity.getSettlementCycleBegin();
        LocalDateTime settlementCycleEnd = queryEntity.getSettlementCycleEnd();
        BigDecimal currentIncome = queryEntity.getCurrentIncome();
        BigDecimal currentExpenditure = queryEntity.getCurrentExpenditure();
        BigDecimal currentCircularDeposit = queryEntity.getCurrentCircularDeposit();
        BigDecimal previousCircularDeposit = queryEntity.getPreviousCircularDeposit();
        BigDecimal circularDepositRatio = queryEntity.getCircularDepositRatio();
        BigDecimal currentTotalAmount = queryEntity.getCurrentTotalAmount();
        BillStateEnum billStatus = queryEntity.getBillState();
        GenerateStateEnum generateStatus = queryEntity.getGenerateState();

        LambdaQueryWrapper<Bill> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotNull(previousBillId), Bill::getPreviousBillId, previousBillId)
            .eq(ObjectUtil.isNotNull(tenantId), Bill::getTenantId, tenantId)
            .eq(StrUtil.isNotBlank(billNo), Bill::getBillNo, billNo)
            .eq(StrUtil.isNotBlank(billCycleNo), Bill::getBillCycleNo, billCycleNo)
            .eq(ObjectUtil.isNotNull(settlementDateTime), Bill::getSettlementDateTime, settlementDateTime)
            .eq(ObjectUtil.isNotNull(settlementCycleBegin), Bill::getSettlementCycleBegin, settlementCycleBegin)
            .eq(ObjectUtil.isNotNull(settlementCycleEnd), Bill::getSettlementCycleEnd, settlementCycleEnd)

            .eq(ObjectUtil.isNotNull(currentIncome), Bill::getCurrentIncome, currentIncome)
            .eq(ObjectUtil.isNotNull(currentExpenditure), Bill::getCurrentExpenditure, currentExpenditure)
            .eq(ObjectUtil.isNotNull(currentCircularDeposit), Bill::getCurrentCircularDeposit, currentCircularDeposit)
            .eq(ObjectUtil.isNotNull(previousCircularDeposit), Bill::getPreviousCircularDeposit, previousCircularDeposit)

            .eq(ObjectUtil.isNotNull(circularDepositRatio), Bill::getCircularDepositRatio, circularDepositRatio)
            .eq(ObjectUtil.isNotNull(currentTotalAmount), Bill::getCurrentTotalAmount, currentTotalAmount)
            .eq(ObjectUtil.isNotNull(billStatus), Bill::getBillState, billStatus)
            .eq(ObjectUtil.isNotNull(generateStatus), Bill::getGenerateState, generateStatus);

        return queryWrapper;
    }

    @InMethodLog("根据周期编号和账单状态查询账单")
    public List<Bill> queryByBillCycleNoAndBillStatus(String billCycleNo, BillStateEnum billState) {
        return lambdaQuery().eq(Bill::getBillCycleNo, billCycleNo).eq(Bill::getBillState, billState).list();
    }

}




