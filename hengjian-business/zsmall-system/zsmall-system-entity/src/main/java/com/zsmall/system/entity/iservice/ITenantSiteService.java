package com.zsmall.system.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zsmall.system.entity.domain.TenantSite;
import com.zsmall.system.entity.mapper.TenantSiteMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


/**
 * 租户站点中间Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-02-25
 */
@RequiredArgsConstructor
@Service
public class ITenantSiteService extends ServiceImpl<TenantSiteMapper, TenantSite> {

    private final TenantSiteMapper baseMapper;

    /**
     * 根据租户id和国家代码查询
     * @param tenantId
     * @param countryCode
     * @return
     */
    public TenantSite getByTenantIdAndCountryCode(String tenantId,String countryCode){
        LambdaQueryWrapper<TenantSite> lqw = Wrappers.lambdaQuery();
        lqw.eq(TenantSite::getTenantId,tenantId);
        lqw.eq(TenantSite::getCountryCode,countryCode);
        return baseMapper.selectOne(lqw);
    }

}
