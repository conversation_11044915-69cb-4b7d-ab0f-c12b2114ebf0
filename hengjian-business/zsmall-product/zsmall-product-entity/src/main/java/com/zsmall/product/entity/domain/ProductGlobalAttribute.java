package com.zsmall.product.entity.domain;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.hengjian.common.tenant.core.NoDeptTenantEntity;
import com.zsmall.common.enums.product.AttributeBelongEnum;
import com.zsmall.common.enums.product.AttributeScopeEnum;
import com.zsmall.common.enums.product.BindingCategoryEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 商品全局属性对象 product_global_attribute
 *
 * <AUTHOR>
 * @date 2023-05-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "product_global_attribute", autoResultMap = true)
public class ProductGlobalAttribute extends NoDeptTenantEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 属性归属（平台、供货商自定义等）
     */
    private AttributeBelongEnum attributeBelong;

    /**
     * 属性名称（主名称）
     */
    private String attributeName;

    /**
     * 属性其他语种名称（格式：key-语种，value-对应语种分类名）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONObject attributeOtherName;

    /**
     * 属性值（字符串数组）
     */
    @TableField(typeHandler = JacksonTypeHandler.class, updateStrategy = FieldStrategy.IGNORED)
    private JSONArray attributeValues;

    /**
     * 属性备注
     */
    private String attributeNotes;

    /**
     * 属性作用域（所有场景、仅通用规格，仅可选规格等）
     */
    private AttributeScopeEnum attributeScope;

    /**
     * 属性状态（0-停用，1-启用等）
     */
    private Integer attributeState;

    /**
     * 是否支持自定义值（0-否，1-是）
     */
    private Boolean isSupportCustom;

    /**
     * 是否是基础属性（0-否，1-是）
     */
    private Boolean isBasicAttribute;

    /**
     * 是否必填
     */
    private Boolean isRequired;

    /**
     * 绑定分类（AllCategory-所有分类，SpecifyCategory-指定分类）
     */
    private BindingCategoryEnum bindingCategory;

    @TableField(exist = false)
    private List<Long> specifyCategoryIds;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
