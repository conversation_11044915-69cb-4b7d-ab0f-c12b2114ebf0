package com.zsmall.system.entity.iservice;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.log.annotation.InMethodLog;
import com.zsmall.common.enums.bill.AbstractTypeEnum;
import com.zsmall.system.entity.domain.BillAbstract;
import com.zsmall.system.entity.mapper.BillAbstractMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【bill_abstract(账单摘要表)】的数据库操作Service实现
 * @createDate 2022-11-28 19:39:05
 */
@Slf4j
@Service
public class IBillAbstractService extends ServiceImpl<BillAbstractMapper, BillAbstract> {

    @InMethodLog("根据账单id查询摘要")
    public List<BillAbstract> queryByBillId(Long billId) {
        return lambdaQuery().eq(BillAbstract::getBillId, billId).list();
    }

    @InMethodLog("根据账单id和摘要类型查询，数据库无法查询到将会返回一个new对象")
    public BillAbstract queryByBillIdAndAbstractType(Long billId, AbstractTypeEnum abstractType) {
        BillAbstract result;
        result = lambdaQuery().eq(BillAbstract::getBillId, billId).eq(BillAbstract::getAbstractType, abstractType).one();

        if (result == null) {
            result = new BillAbstract();
            result.setBillId(billId);
            result.setAbstractType(abstractType);
            result.setAbstractTotalAmount(BigDecimal.ZERO);
        }
        return result;
    }
}




