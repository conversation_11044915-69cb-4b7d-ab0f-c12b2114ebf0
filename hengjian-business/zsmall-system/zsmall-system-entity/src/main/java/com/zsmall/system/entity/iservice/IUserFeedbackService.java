package com.zsmall.system.entity.iservice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.log.annotation.InMethodLog;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.system.entity.domain.UserFeedback;
import com.zsmall.system.entity.domain.bo.userFeedback.UserFeedbackPageBo;
import com.zsmall.system.entity.mapper.UserFeedbackMapper;
import org.springframework.stereotype.Service;
/**
 * 用户反馈-数据库接口层
 * <AUTHOR>
 * @date 2023/9/11
 */
@Service
public class IUserFeedbackService extends ServiceImpl<UserFeedbackMapper, UserFeedback> {

    @InMethodLog("分页查询用户反馈")
    public IPage<UserFeedback> queryPage(UserFeedbackPageBo bo, Page<UserFeedback> page) {
        return TenantHelper.ignore(() -> baseMapper.queryPage(bo, page), TenantType.Manager);
    }

}
